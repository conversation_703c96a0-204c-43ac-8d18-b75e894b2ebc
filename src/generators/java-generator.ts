import { ParsedClass, ParsedField, JavaGenerationOptions } from '../types/index';
import { ensureDirectoryExists, writeFileContent } from '../utils/file-utils';
import * as path from 'path';

/**
 * Java 类代码生成器
 */
export class JavaCodeGenerator {
  private options: JavaGenerationOptions;

  constructor(options: JavaGenerationOptions) {
    this.options = options;
  }

  /**
   * 生成所有 Java 类
   */
  generateAllClasses(classes: ParsedClass[]): void {
    for (const parsedClass of classes) {
      this.generateClass(parsedClass);
    }
  }

  /**
   * 生成单个 Java 类
   */
  generateClass(parsedClass: ParsedClass): void {
    const javaCode = this.generateJavaCode(parsedClass);
    const filePath = this.getOutputFilePath(parsedClass.name);
    
    writeFileContent(filePath, javaCode);
    console.log(`Generated Java class: ${filePath}`);
  }

  /**
   * 生成 Java 代码字符串
   */
  private generateJavaCode(parsedClass: ParsedClass): string {
    const imports = this.generateImports(parsedClass);
    const classDeclaration = this.generateClassDeclaration(parsedClass);
    const fields = this.generateFields(parsedClass);
    const constructors = this.generateConstructors(parsedClass);
    const gettersAndSetters = this.generateGettersAndSetters(parsedClass);
    const toString = this.generateToString(parsedClass);

    return `${this.generatePackageDeclaration()}
${imports}

/**
 * Generated from XSD schema
 * ${parsedClass.namespace ? `Namespace: ${parsedClass.namespace}` : ''}
 * ${parsedClass.isRootElement ? 'Root Element' : 'Complex Type'}
 */
${classDeclaration} {

${fields}
${constructors}
${gettersAndSetters}
${toString}
}
`;
  }

  /**
   * 生成包声明
   */
  private generatePackageDeclaration(): string {
    return `package ${this.options.packageName};`;
  }

  /**
   * 生成导入语句
   */
  private generateImports(parsedClass: ParsedClass): string {
    const imports = new Set<string>();

    // 检查是否需要 List 导入
    for (const field of parsedClass.fields) {
      if (field.isArray) {
        imports.add('import java.util.List;');
        imports.add('import java.util.ArrayList;');
      }
      
      // 检查日期类型
      if (field.javaType.includes('LocalDate')) {
        imports.add('import java.time.LocalDate;');
      }
      if (field.javaType.includes('LocalDateTime')) {
        imports.add('import java.time.LocalDateTime;');
      }
      if (field.javaType.includes('LocalTime')) {
        imports.add('import java.time.LocalTime;');
      }
      if (field.javaType.includes('BigDecimal')) {
        imports.add('import java.math.BigDecimal;');
      }
    }

    // JSR-303 验证注解 (Jakarta EE for Spring Boot 3.x)
    if (this.options.useJSR303Validation) {
      imports.add('import jakarta.validation.constraints.*;');
      imports.add('import jakarta.validation.Valid;');
    }

    // Jackson 注解
    if (this.options.useJacksonAnnotations) {
      imports.add('import com.fasterxml.jackson.annotation.*;');
    }

    // Lombok 注解
    if (this.options.useLombok) {
      imports.add('import lombok.*;');
    }

    return Array.from(imports).sort().join('\n');
  }

  /**
   * 生成类声明
   */
  private generateClassDeclaration(parsedClass: ParsedClass): string {
    const annotations: string[] = [];

    if (this.options.useLombok) {
      annotations.push('@Data');
      annotations.push('@NoArgsConstructor');
      annotations.push('@AllArgsConstructor');
    }

    if (this.options.useJacksonAnnotations) {
      if (parsedClass.isRootElement) {
        annotations.push(`@JsonRootName("${parsedClass.name}")`);
      }
      annotations.push('@JsonIgnoreProperties(ignoreUnknown = true)');
    }

    const annotationString = annotations.length > 0 
      ? annotations.join('\n') + '\n'
      : '';

    return `${annotationString}public class ${parsedClass.name}`;
  }

  /**
   * 生成字段声明
   */
  private generateFields(parsedClass: ParsedClass): string {
    if (this.options.useLombok) {
      // 如果使用 Lombok，只生成字段声明
      return parsedClass.fields
        .map(field => this.generateFieldDeclaration(field))
        .join('\n\n');
    }

    return parsedClass.fields
      .map(field => this.generateFieldDeclaration(field))
      .join('\n\n');
  }

  /**
   * 生成单个字段声明
   */
  private generateFieldDeclaration(field: ParsedField): string {
    const annotations: string[] = [];

    // JSR-303 验证注解
    if (this.options.useJSR303Validation) {
      if (!field.isOptional) {
        annotations.push('    @NotNull');
      }
      if (field.javaType === 'String' && !field.isOptional) {
        annotations.push('    @NotBlank');
      }
      if (field.isComplexType) {
        annotations.push('    @Valid');
      }
    }

    // Jackson 注解
    if (this.options.useJacksonAnnotations) {
      annotations.push(`    @JsonProperty("${field.name}")`);
    }

    const annotationString = annotations.length > 0 
      ? annotations.join('\n') + '\n'
      : '';

    const defaultValue = this.generateDefaultValue(field);
    const fieldDeclaration = `    private ${field.javaType} ${field.name}${defaultValue};`;

    return `${annotationString}${fieldDeclaration}`;
  }

  /**
   * 生成默认值
   */
  private generateDefaultValue(field: ParsedField): string {
    if (field.isArray) {
      return ' = new ArrayList<>()';
    }
    return '';
  }

  /**
   * 生成构造函数
   */
  private generateConstructors(parsedClass: ParsedClass): string {
    if (this.options.useLombok || !this.options.includeConstructors) {
      return '';
    }

    const defaultConstructor = `
    /**
     * Default constructor
     */
    public ${parsedClass.name}() {
    }`;

    const parameterizedConstructor = this.generateParameterizedConstructor(parsedClass);

    return defaultConstructor + '\n' + parameterizedConstructor;
  }

  /**
   * 生成带参数的构造函数
   */
  private generateParameterizedConstructor(parsedClass: ParsedClass): string {
    if (parsedClass.fields.length === 0) {
      return '';
    }

    const parameters = parsedClass.fields
      .map(field => `${field.javaType} ${field.name}`)
      .join(', ');

    const assignments = parsedClass.fields
      .map(field => `        this.${field.name} = ${field.name};`)
      .join('\n');

    return `
    /**
     * Parameterized constructor
     */
    public ${parsedClass.name}(${parameters}) {
${assignments}
    }`;
  }

  /**
   * 生成 Getter 和 Setter 方法
   */
  private generateGettersAndSetters(parsedClass: ParsedClass): string {
    if (this.options.useLombok) {
      return '';
    }

    return parsedClass.fields
      .map(field => this.generateGetterAndSetter(field))
      .join('\n\n');
  }

  /**
   * 生成单个字段的 Getter 和 Setter
   */
  private generateGetterAndSetter(field: ParsedField): string {
    const capitalizedName = field.name.charAt(0).toUpperCase() + field.name.slice(1);
    
    const getter = `
    /**
     * Get ${field.name}
     */
    public ${field.javaType} get${capitalizedName}() {
        return ${field.name};
    }`;

    const setter = `
    /**
     * Set ${field.name}
     */
    public void set${capitalizedName}(${field.javaType} ${field.name}) {
        this.${field.name} = ${field.name};
    }`;

    return getter + '\n' + setter;
  }

  /**
   * 生成 toString 方法
   */
  private generateToString(parsedClass: ParsedClass): string {
    if (this.options.useLombok || !this.options.includeToString) {
      return '';
    }

    const fields = parsedClass.fields
      .map(field => `"${field.name}=" + ${field.name}`)
      .join(' + ", " + ');

    return `
    @Override
    public String toString() {
        return "${parsedClass.name}{" +
                ${fields} +
                "}";
    }`;
  }

  /**
   * 获取输出文件路径
   */
  private getOutputFilePath(className: string): string {
    const packagePath = this.options.packageName.replace(/\./g, path.sep);
    const fileName = `${className}.java`;
    return path.join(this.options.outputDir, packagePath, fileName);
  }
}
