package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Root Element
 */
@JsonRootName("Client4XXError")
@JsonIgnoreProperties(ignoreUnknown = true)
public class Client4XXError {



    /**
     * Default constructor
     */
    public Client4XXError() {
    }



    @Override
    public String toString() {
        return "Client4XXError{" +
                 +
                "}";
    }
}
