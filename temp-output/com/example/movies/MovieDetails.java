package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://www.example.org/MovieCatalogMaster
 * Root Element
 */
@JsonRootName("MovieDetails")
@JsonIgnoreProperties(ignoreUnknown = true)
public class MovieDetails {



    /**
     * Default constructor
     */
    public MovieDetails() {
    }



    @Override
    public String toString() {
        return "MovieDetails{" +
                 +
                "}";
    }
}
