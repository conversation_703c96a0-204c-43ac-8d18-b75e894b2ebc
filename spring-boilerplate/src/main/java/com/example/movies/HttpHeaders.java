package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Root Element
 */
@JsonRootName("HttpHeaders")
@JsonIgnoreProperties(ignoreUnknown = true)
public class HttpHeaders {



    /**
     * Default constructor
     */
    public HttpHeaders() {
    }



    @Override
    public String toString() {
        return "HttpHeaders{" +
                 +
                "}";
    }
}
