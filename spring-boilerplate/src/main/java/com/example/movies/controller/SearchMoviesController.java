package com.example.movies.controller;

import com.example.movies.model.OMDBSearchElement;
import com.example.movies.service.SearchMoviesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST Controller for Movie Search Operations
 * Generated from TIBCO BW Process: SearchMovies
 */
@RestController
@RequestMapping("/api")
public class SearchMoviesController {

    @Autowired
    private SearchMoviesService searchMoviesService;

    /**
     * Search movies by search string
     *
     * @param searchString The search query string
     * @return ResponseEntity containing search results
     */
    @GetMapping("/movies")
    public ResponseEntity<OMDBSearchElement> searchMovies(
            @RequestParam("searchString") String searchString) {

        try {
            OMDBSearchElement result = searchMoviesService.searchMovies(searchString, null);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
