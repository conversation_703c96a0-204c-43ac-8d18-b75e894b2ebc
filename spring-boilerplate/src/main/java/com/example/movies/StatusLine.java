package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Root Element
 */
@JsonRootName("StatusLine")
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatusLine {



    /**
     * Default constructor
     */
    public StatusLine() {
    }



    @Override
    public String toString() {
        return "StatusLine{" +
                 +
                "}";
    }
}
