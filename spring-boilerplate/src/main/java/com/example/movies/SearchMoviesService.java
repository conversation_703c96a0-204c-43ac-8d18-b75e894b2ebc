package com.example.movies;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class SearchMoviesService {
    private static final Logger logger = LoggerFactory.getLogger(SearchMoviesService.class);
        @Autowired
    private RestTemplate restTemplate;
        public OMDBSearchElement get(String searchString) {
        logger.info("Executing get with parameters: {}", searchString);

        try {
            // For demo purposes, return a mock response
            // In real implementation, this would call external OMDB API
            // Example: restTemplate.getForObject("http://www.omdbapi.com/?s=" + searchString + "&apikey=YOUR_API_KEY", OMDBSearchElement.class);
            OMDBSearchElement result = new OMDBSearchElement();
            // TODO: Set mock data or call external API

            logger.info("Successfully completed get");
            return result;
        } catch (Exception e) {
            logger.error("Error in get: " + e.getMessage(), e);
            throw new RuntimeException("Service call failed", e);
        }
    }
}