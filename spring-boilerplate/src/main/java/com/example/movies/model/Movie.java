package com.example.movies.model;
import com.fasterxml.jackson.annotation.*;
import java.util.ArrayList;
import java.util.List;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://www.example.org/MovieCatalogMaster
 * Root Element
 */
@JsonRootName("Movie")
@JsonIgnoreProperties(ignoreUnknown = true)
public class Movie {

    @Valid
    @JsonProperty("movies")
    private List<MoviesType> movies = new ArrayList<>();

    /**
     * Default constructor
     */
    public Movie() {
    }

    /**
     * Parameterized constructor
     */
    public Movie(List<MoviesType> movies) {
        this.movies = movies;
    }

    /**
     * Get movies
     */
    public List<MoviesType> getMovies() {
        return movies;
    }

    /**
     * Set movies
     */
    public void setMovies(List<MoviesType> movies) {
        this.movies = movies;
    }

    @Override
    public String toString() {
        return "Movie{" +
                "movies=" + movies +
                "}";
    }
}
